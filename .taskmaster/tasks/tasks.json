{"master": {"tasks": [{"id": 1, "title": "调整首页Logo大小", "description": "修复首页“中国烟草”Logo显示尺寸不当的问题，优化视觉效果。", "details": "涉及前端UI调整。需要修改相关Vue组件的CSS，确保Logo在不同分辨率下都能按比例正确显示，避免拉伸或过大过小。", "testStrategy": "在多种主流浏览器和不同屏幕分辨率下访问首页，目视检查Logo显示是否正常、美观。", "priority": "low", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "定位并分析Logo组件及相关CSS", "description": "在项目中找到负责渲染首页“中国烟草”Logo的Vue组件，并使用浏览器开发者工具分析当前应用的CSS样式，确定导致尺寸不当的具体原因。", "dependencies": [], "details": "重点排查Header或Layout相关的组件。检查是否存在固定的像素值（px）或不当的百分比设置。", "status": "pending", "testStrategy": "确认已定位到唯一的Vue组件和CSS规则文件。"}, {"id": 2, "title": "修改CSS以实现响应式尺寸", "description": "修改已定位的CSS代码，移除固定的尺寸设置，改用相对单位（如vw, %）或结合媒体查询（@media queries）来定义Logo大小，确保其能随屏幕宽度变化而等比缩放。", "dependencies": ["1.1"], "details": "建议为Logo容器设置一个最大宽度（max-width），并让图片高度自动（height: auto）以保持其原始宽高比，防止拉伸。", "status": "pending", "testStrategy": "在本地开发环境，拖动浏览器窗口大小，观察Logo是否平滑、按比例缩放。"}, {"id": 3, "title": "跨浏览器与多分辨率兼容性测试", "description": "在多种主流浏览器（如Chrome, Firefox, Edge）上进行测试，并使用开发者工具模拟常见桌面、平板和移动设备的分辨率，验证Logo在各种场景下均能正常、美观地显示。", "dependencies": ["1.2"], "details": "测试分辨率应覆盖小（如375px）、中（如768px）、大（如1920px）等典型宽度。特别注意检查高分屏下的显示清晰度。", "status": "pending", "testStrategy": "逐一在目标浏览器和分辨率下进行目视检查，截图记录正常与异常情况。"}, {"id": 4, "title": "代码审查与合并", "description": "将修改后的代码提交至代码仓库，创建一个合并请求（Pull Request），并附上修改前后的对比截图和测试说明，待同事审查通过后，将代码合并到主开发分支。", "dependencies": ["1.3"], "details": "在合并请求中清晰说明解决的问题和采用的技术方案（如使用了媒体查询或flex布局等）。", "status": "pending", "testStrategy": "确认代码合并后，CI/CD流程通过，并且在预发布环境中再次验证首页Logo显示正常。"}]}, {"id": 2, "title": "移除首页账号后的英文角色名", "description": "清理首页用户账号信息后多余的英文角色标识（如 'employee'），提升界面整洁度。", "details": "在前端Vue组件中，找到渲染用户信息的模板部分，移除显示英文角色名的代码逻辑。", "testStrategy": "分别使用employee, county_reviewer, city_reviewer等不同角色的账号登录系统，检查首页右上角用户信息处是否已不再显示英文角色名。", "priority": "low", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "定位渲染用户信息的Vue组件", "description": "在前端项目代码中，通过分析页面结构或搜索关键字，精确定位负责在首页右上角显示用户账号和角色名的Vue组件。", "dependencies": [], "details": "重点检查项目中名为 `Header`、`UserInfo` 或 `ProfileDropdown` 等通用布局组件。可使用浏览器开发者工具的Vue插件来快速识别组件名称。", "status": "pending", "testStrategy": "在浏览器中打开首页，使用开发者工具检查右上角的用户信息元素，确认其所属的Vue组件名。"}, {"id": 2, "title": "移除模板中的角色名显示逻辑", "description": "在定位到的Vue组件的模板（template）部分，删除或注释掉用于渲染英文角色名的HTML元素及相关的Vue指令或插值表达式。", "dependencies": ["2.1"], "details": "查找并移除类似 `<span>({{ user.role }})</span>` 或 `<div>{{ roleName }}</div>` 的代码片段。注意不要误删显示用户名的代码。", "status": "pending", "testStrategy": "在本地开发环境中修改代码后，热重载页面，目视检查角色名是否已从界面上消失。"}, {"id": 3, "title": "清理组件脚本中不再需要的逻辑", "description": "检查并移除组件的JavaScript（script）部分中，仅为显示角色名而存在的、现已不再需要的数据属性、计算属性或方法。", "dependencies": ["2.2"], "details": "如果之前有专门用于格式化或获取角色名的 `computed` 属性或 `method`，在移除模板引用后，应将其一并删除，以保持代码整洁。", "status": "pending", "testStrategy": "在本地开发环境，使用不同角色账号（如 employee, county_reviewer）登录，确认移除逻辑后，用户信息显示正常且浏览器控制台无相关错误。"}, {"id": 4, "title": "提交代码并准备测试验证", "description": "将修改后的代码提交到版本控制系统，并创建合并请求（Pull Request），清晰说明本次修改的目的，以便团队审查和部署到测试环境。", "dependencies": ["2.3"], "details": "编写规范的提交信息，如 `feat(UI): Remove English role name from homepage header`。在合并请求中关联此任务（Task ID: 2）。", "status": "pending", "testStrategy": "通知测试人员，待代码部署到测试环境后，按照任务中定义的测试策略，使用所有相关角色账号登录，验证变更是否生效且无副作用。"}]}, {"id": 3, "title": "修复审核员无法预览合同的问题", "description": "解决县、市局审核员（county_reviewer, city_reviewer）无权限预览合同的bug。", "details": "检查后端关于合同预览的API接口权限控制，确保为县、市局审核员角色正确授权。可能也涉及前端按钮的显示逻辑修复。", "testStrategy": "使用县局审核员和市局审核员账号登录，进入合同列表，点击任意合同的“预览”按钮，验证是否能成功打开并查看合同内容。", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "后端权限排查：分析合同预览API的访问控制逻辑", "description": "深入检查负责合同预览的后端API接口，定位并分析其权限验证代码，明确为何 county_reviewer 和 city_reviewer 角色被拒绝访问。", "dependencies": [], "details": "重点审查与该API相关的安全中间件、权限注解或访问控制列表（ACL）配置。使用API测试工具（如Postman）模拟审核员角色请求，以复现权限错误，并记录导致失败的具体代码段。", "status": "done", "testStrategy": "使用API测试工具，携带 county_reviewer 角色的认证token，请求合同预览接口，预期收到 403 Forbidden 或类似的权限错误响应。"}, {"id": 2, "title": "后端权限修复：为审核员角色授予预览接口权限", "description": "根据上一步的分析结果，修改后端代码，将 county_reviewer 和 city_reviewer 角色添加到合同预览API的授权列表中。", "dependencies": [], "details": "在相应的权限控制代码中（例如 `hasAnyRole('admin', ...)`）添加缺失的审核员角色。确保修改仅限于预览接口，避免引入其他安全风险。提交代码后，需更新或编写单元测试以覆盖新授权的场景。", "status": "done", "testStrategy": "在本地或开发环境，再次使用API测试工具模拟审核员角色请求，验证接口是否返回 200 OK 及合同数据。"}, {"id": 3, "title": "前端界面检查：验证并修复预览按钮的显示逻辑", "description": "检查合同列表页面的前端代码，确保“预览”按钮对 county_reviewer 和 city_reviewer 角色是始终可见且可点击的。", "dependencies": [], "details": "审查相关Vue组件中控制“预览”按钮的条件渲染指令（如 `v-if` 或 `v-show`）或计算属性。修正任何可能存在的、错误地隐藏或禁用该按钮的角色判断逻辑。", "status": "done", "testStrategy": "在前端开发环境中，通过模拟登录或硬编码方式切换到审核员角色，目视检查合同列表中的“预览”按钮是否按预期显示。"}, {"id": 4, "title": "端到端集成测试：验证审核员预览合同全流程", "description": "在部署了前后端修复代码的测试环境中，进行完整的用户场景测试，确认问题已彻底解决。", "dependencies": [], "details": "分别使用真实的 county_reviewer 和 city_reviewer 测试账号登录系统。导航至合同列表，随机选取几份合同，点击“预览”按钮，确认能够无权限错误地成功加载并完整查看合同内容。\n<info added on 2025-07-28T06:45:46.834Z>\n✅ 县局审核员 (county_reviewer1) 测试通过：\n- 成功登录系统\n- 成功获取合同列表（4份合同）\n- 成功预览3份合同，无权限错误\n\n✅ 市局审核员 (city_reviewer1) 测试通过：\n- 成功登录系统\n- 成功获取合同列表（0份合同，符合预期）\n- 权限检查正常工作\n\n测试证明修复成功：\n1. 后端RBAC权限系统正常工作\n2. 前端权限检查逻辑正确\n3. 县局和市局审核员都能正常预览分配给他们的合同\n4. 权限控制精确，只能预览分配给自己的合同\n\nBug已彻底解决！\n</info added on 2025-07-28T06:45:46.834Z>", "status": "done", "testStrategy": "遵循任务描述中的官方测试策略：使用县局和市局审核员账号登录，进入合同列表，点击任意合同的“预览”按钮，验证是否能成功打开并查看合同内容。"}]}, {"id": 4, "title": "在合同预览界面增加审核功能按钮", "description": "优化审核流程，允许县、市局审核员在预览合同的同时直接进行“通过”或“驳回”操作，无需关闭预览。", "details": "修改前端合同预览的Vue组件，在界面上添加“审核通过”和“审核驳回”按钮，并绑定相应的API调用，操作完成后应自动关闭预览并刷新列表。", "testStrategy": "作为审核员，在预览合同界面点击“审核通过”或“审核驳回”按钮，验证合同状态是否被正确更新，以及预览窗口是否关闭。", "priority": "medium", "dependencies": [3], "status": "pending", "subtasks": [{"id": 1, "title": "在合同预览组件中添加审核操作按钮UI", "description": "修改合同预览的Vue组件，在界面上增加“审核通过”和“审核驳回”两个按钮，并完成基础的UI布局和样式。", "dependencies": [], "details": "在合同预览组件的模板（template）部分，添加两个按钮元素。可以考虑将它们放置在预览窗口的页脚或一个固定的操作栏中，确保在滚动预览内容时按钮始终可见。此阶段只关注UI展示，无需绑定功能。", "status": "pending", "testStrategy": "打开任意合同的预览界面，目视检查“审核通过”和“审核驳回”按钮是否存在，且位置和样式符合设计要求。"}, {"id": 2, "title": "实现“审核通过”按钮的功能逻辑", "description": "为“审核通过”按钮绑定点击事件，使其能够调用后端的审核通过API接口。", "dependencies": ["4.1"], "details": "在Vue组件的脚本（script）部分，为“审核通过”按钮创建一个点击事件处理方法。该方法需获取当前预览合同的ID，并向后端发送审核通过的API请求。需要考虑API调用期间的加载状态处理，例如临时禁用按钮。", "status": "pending", "testStrategy": "以审核员身份登录，打开待审合同预览，点击“审核通过”按钮。使用浏览器开发者工具的网络面板，确认系统向正确的API端点发送了请求，并携带了正确的合同ID。"}, {"id": 3, "title": "实现“审核驳回”按钮的功能逻辑", "description": "为“审核驳回”按钮绑定点击事件，点击后弹出对话框要求输入驳回理由，确认后调用后端的审核驳回API接口。", "dependencies": ["4.1"], "details": "为“审核驳回”按钮创建点击事件处理方法。该方法应触发一个模态框（Modal/Dialog），其中包含一个文本输入区供用户填写驳回理由。点击模态框的确认按钮后，将合同ID和驳回理由一并发送给后端的驳回API。", "status": "pending", "testStrategy": "点击“审核驳回”按钮，验证是否弹出理由输入框。输入理由并提交，检查网络请求是否将合同ID和填写的理由正确发送到驳回API。"}, {"id": 4, "title": "实现操作成功后的自动关闭和列表刷新", "description": "在“审核通过”或“审核驳回”API调用成功后，实现自动关闭合同预览窗口并刷新合同列表的功能。", "dependencies": ["4.2", "4.3"], "details": "在处理审核API成功响应的回调逻辑中（例如Promise的.then()或async/await的try块），添加两步操作：1. 调用方法关闭当前的预览组件（Modal或页面）。2. 通知父组件（合同列表页）刷新数据，可以通过`$emit`一个自定义事件来触发。", "status": "pending", "testStrategy": "完整地执行一次“审核通过”或“审核驳回”操作。验证操作成功后，预览窗口是否自动关闭，同时背景的合同列表是否刷新，并且对应合同的状态已更新。"}]}, {"id": 5, "title": "调整“审核通过”说明的字数限制", "description": "当前系统要求“审核通过”的说明必须超过10个字，此限制不合理，需要调整或移除。", "details": "检查前端和后端的表单验证逻辑，将“审核通过”说明的最小字数限制移除或设置为0，使其变为可选填写项。", "testStrategy": "作为审核员，在审核合同时，不填写任何通过说明或填写少于10个字的说明，验证系统是否能成功提交审核。", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "移除前端表单的字数验证规则", "description": "定位到处理合同审核功能的前端Vue组件，找到“审核通过”说明输入框的表单验证逻辑，并移除其最小10个字的限制。", "dependencies": [], "details": "需要修改与审核操作相关的模态框（Modal）或表单页面的代码，将输入框的 `rules` 属性或相关的验证函数进行调整，使其不再进行最小长度校验，变为可选填写。", "status": "pending", "testStrategy": "在本地开发环境，尝试提交审核表单时不填写任何说明，验证前端不再拦截或提示错误。"}, {"id": 2, "title": "移除后端API接口的字数验证逻辑", "description": "检查处理合同审核提交的后端API接口，修改或移除其对“审核通过”说明字段（remark/description等）的服务器端验证逻辑。", "dependencies": [], "details": "在后端的Controller层或Service层，找到接收审核数据的DTO（Data Transfer Object）或请求参数，移除相关的验证注解（如 @Size(min=10) 或 @NotBlank）或手动验证代码。", "status": "pending", "testStrategy": "使用API测试工具（如Postman）直接向审核接口发送请求，请求体中不包含说明字段或说明字段为空字符串，验证接口是否能返回200成功状态码。"}, {"id": 3, "title": "执行端到端功能测试", "description": "在集成环境中，以审核员角色登录，完整地测试审核通过流程，确保在不填写或填写简短说明的情况下，审核能够成功提交。", "dependencies": ["5.1", "5.2"], "details": "测试场景需覆盖两种情况：1. 不填写任何说明直接点击“审核通过”。 2. 填写少于10个字的说明（例如“同意”）后点击“审核通过”。", "status": "pending", "testStrategy": "操作成功后，刷新合同列表或查看合同详情，确认合同状态已更新。同时，检查数据库中对应合同记录的说明字段是否已正确保存（分别为NULL/空字符串或“同意”）。"}, {"id": 4, "title": "代码审查与合并", "description": "为前端和后端的代码修改创建合并请求（Pull Request），安排团队成员进行代码审查（Code Review），通过后合并到主开发分支。", "dependencies": ["5.3"], "details": "审查要点是确认前后端的相关验证逻辑均已彻底移除，且未对其他业务逻辑（如“审核驳回”时说明为必填）产生非预期的影响。合并前需确保所有自动化测试通过。", "status": "pending", "testStrategy": ""}]}, {"id": 6, "title": "实现县局到市局的审核流转机制", "description": "建立合同在县局审核通过后，自动流转至市局进行下一步审核的业务流程。", "details": "修改后端审核通过的业务逻辑。当县局审核员批准后，系统需自动更新合同状态为“待市局审核”，并使其对市局审核员可见。", "testStrategy": "1. 县局审核员登录并审核通过一份合同。 2. 市局审核员登录，验证该合同是否出现在其待办列表中，且状态为“待市局审核”。", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "在合同模型中添加‘待市局审核’状态", "description": "在后端的合同实体类和数据库的status字段中，增加一个新的枚举值或字符串值‘待市局审核’，为后续的流程流转提供基础状态支持。", "dependencies": [], "details": "可能涉及修改数据库迁移文件、实体类（如Java/Python的Model）以及相关的状态常量或枚举定义文件，确保新状态在整个系统中可识别。", "status": "done", "testStrategy": "执行数据库迁移并启动后端服务，通过代码或数据库客户端确认合同表结构已更新，且应用程序代码中可以无误地引用此新状态。"}, {"id": 2, "title": "修改审核批准接口以实现状态流转", "description": "重构处理合同批准的后端API。当检测到操作者为县局审核员时，系统应将合同状态从‘待县局审核’更新为‘待市局审核’，而非直接设为最终批准状态。", "dependencies": ["6.1"], "details": "在审核服务的核心方法（如 `approveContract`）中，加入基于用户角色的条件判断逻辑。如果当前用户角色是 `county_reviewer`，则执行状态更新为‘待市局审核’的逻辑。", "status": "done", "testStrategy": "使用县局审核员的认证信息，通过API测试工具（如Postman）调用审核批准接口，验证数据库中对应合同的状态字段是否已正确更新为‘待市局审核’。"}, {"id": 3, "title": "调整合同列表查询逻辑以适配市局角色", "description": "修改获取待办合同列表的API，确保市局审核员登录后，其待办列表能正确查询并展示所有状态为‘待市局审核’的合同。", "dependencies": ["6.1"], "details": "在合同数据访问层（Repository/DAO）的查询方法中，为 `city_reviewer` 角色增加对‘待市局审核’状态的查询条件，确保权限隔离和数据准确性。", "status": "done", "testStrategy": "使用市局审核员的认证信息，调用获取待办合同列表的API，检查返回结果中是否包含了此前由县局提交的、状态为‘待市局审核’的合同。"}, {"id": 4, "title": "增加县局到市局的审核流转日志记录", "description": "在县局审核员批准并将合同流转至市局时，在审核历史或操作日志中创建一条新记录，用于追踪合同的完整生命周期。", "dependencies": ["6.2"], "details": "在审核批准的业务逻辑成功更新合同状态后，调用日志服务，记录下操作人、操作时间、原始状态、新状态（‘待市局审核’）以及操作类型（如‘提交至市局’）。", "status": "done", "testStrategy": "在县局审核员完成审核操作后，查询对应合同的审核历史记录，验证是否已生成一条内容正确的流转记录，明确指出合同已由县局流转至市局。"}]}, {"id": 7, "title": "修复审核员账号无法看到合同列表的Bug", "description": "解决县、市局审核员登录后，合同列表为空，无法看到待审合同的严重页面Bug。", "details": "重点排查后端获取合同列表的API。检查数据库查询语句和角色权限过滤逻辑，确保能为不同审核员正确返回其权限范围内的合同数据。", "testStrategy": "使用县局和市局审核员账号分别登录，验证其合同列表页是否能正确加载并显示处于相应待审状态的合同。", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "API诊断与日志分析", "description": "复现Bug，使用县、市局审核员账号登录系统，通过浏览器开发者工具定位并检查获取合同列表的API请求，分析其请求参数、响应状态码和返回内容。同时，检查后端服务器日志，查找与该API相关的任何错误或异常堆栈信息。", "dependencies": [], "details": "重点关注网络请求是否成功，返回的数据是否为空数组。日志分析应集中在API的Controller层，寻找潜在的空指针、权限验证失败或数据库连接等问题。", "status": "done", "testStrategy": "使用Chrome/Firefox开发者工具的网络(Network)面板，过滤出目标API调用，确认其返回体(response body)是否为空。"}, {"id": 2, "title": "审查角色权限与数据范围过滤逻辑", "description": "深入后端代码，审查处理合同列表API的Service层。重点检查获取当前登录用户身份（角色、所属机构级别）的逻辑，以及如何利用该身份信息来构建数据查询范围（例如，县级审核员只能看到本县的、状态为“待县级审核”的合同）。", "dependencies": ["7.1"], "details": "核查代码中是否存在硬编码的角色判断错误，或者在获取用户所属区域ID时出错，导致数据过滤条件不正确。确认代码逻辑与业务需求（县/市局审核员的数据可见范围）一致。", "status": "done", "testStrategy": "通过代码走查（Code Review）或在本地调试环境中，单步跟踪该API的执行流程，观察变量值是否符合预期。"}, {"id": 3, "title": "修正数据库查询语句并实现修复", "description": "根据上一步的审查结果，定位并修正数据库查询语句（SQL或ORM查询）。确保查询的WHERE子句能正确地根据用户角色和合同状态进行双重过滤，返回正确的合同列表。", "dependencies": ["7.2"], "details": "例如，查询可能需要添加类似 `WHERE (contract.status = 'PENDING_COUNTY_REVIEW' AND user.role = 'county_reviewer' AND user.region_id = contract.region_id)` 的逻辑。修改代码并提交。", "status": "done", "testStrategy": "在数据库客户端中，手动执行修改前后的SQL语句，对比查询结果，验证新语句的正确性。"}, {"id": 4, "title": "集成测试与手动验证", "description": "在开发或测试环境中部署修复后的代码。严格按照任务的测试策略，分别使用县局和市局审核员的测试账号登录，验证合同列表页现在能够正确加载并显示其权限范围内的待审合同。", "dependencies": ["7.3"], "details": "除了验证问题被修复，还需简单测试其他角色（如普通员工）的合同列表功能是否受影响，确保修复没有引入新的Bug（回归测试）。", "status": "done", "testStrategy": "1. 县局审核员登录，验证列表显示“待县级审核”的合同。2. 市局审核员登录，验证列表显示“待市级审核”的合同。3. 普通员工登录，验证其视图未受影响。"}]}], "metadata": {"created": "2025-07-28T06:06:51.676Z", "updated": "2025-07-28T09:16:54.431Z", "description": "Tasks for master context"}}}